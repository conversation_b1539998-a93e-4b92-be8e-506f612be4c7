/**
 * Superadmin Parts - Best Parts
 * Handles best parts display and filtering on the parts page
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize date range change event listeners
    initDateRangeListeners();

    // Initialize tab functionality for best parts section
    initBestPartsTabs();

    // Initialize best parts settings
    initBestPartsSettings();
});

/**
 * Initialize date range listeners
 */
function initDateRangeListeners() {
    // Get date range form elements
    const dateRangeForm = document.getElementById('dateRangeForm');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const searchBtn = document.getElementById('searchBtn');

    if (dateRangeForm) {
        dateRangeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading state
            showLoadingOverlay();

            // Get date values
            const startDate = startDateInput ? startDateInput.value : '';
            const endDate = endDateInput ? endDateInput.value : '';

            // Build query parameters
            const params = new URLSearchParams(window.location.search);

            if (startDate) params.set('start_date', startDate);
            if (endDate) params.set('end_date', endDate);

            // Redirect to the same page with new parameters
            window.location.href = `${window.location.pathname}?${params.toString()}`;
        });
    }

    // Add event listener to search button if it exists
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            // Trigger form submission
            if (dateRangeForm) dateRangeForm.dispatchEvent(new Event('submit'));
        });
    }

    // Add event listeners to division filter tabs
    const divisionTabs = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');
    divisionTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Get the division from the tab
            const division = this.getAttribute('data-division');

            // Load best parts data for this division
            loadBestPartsData(division);
        });
    });
}

/**
 * Initialize tabs functionality for best parts section
 */
function initBestPartsTabs() {
    // Get all category tabs
    const categoryTabs = document.querySelectorAll('.category-tab');

    // Get all category panes
    const categoryPanes = document.querySelectorAll('.category-pane');

    // Show the first category pane by default
    if (categoryPanes.length > 0) {
        categoryPanes[0].style.display = 'block';
    }

    // Add click event listeners to category tabs
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Get the category from the tab
            const category = this.getAttribute('data-category');

            // Remove active class from all tabs
            categoryTabs.forEach(t => t.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Hide all category panes
            categoryPanes.forEach(pane => {
                pane.style.display = 'none';
            });

            // Show the selected category pane
            const selectedPane = document.getElementById(`category-${category}`);
            if (selectedPane) {
                selectedPane.style.display = 'block';
            }

            // Load best parts data for this category
            loadBestPartsData(category);
        });
    });
}

/**
 * Load best parts data for a specific division
 *
 * @param {string} division - The division to load data for
 */
function loadBestPartsData(division = '') {
    // Show loading overlay
    showLoadingOverlay();

    // Get date range values
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');

    const startDate = startDateInput ? startDateInput.value : '';
    const endDate = endDateInput ? endDateInput.value : '';

    // Build query parameters
    let queryParams = new URLSearchParams();

    // Add date range parameters if available
    if (startDate && endDate) {
        queryParams.append('start_date', startDate);
        queryParams.append('end_date', endDate);
    }

    // Add division filter if provided
    if (division) {
        queryParams.append('division', division);
    }

    console.log('Loading best parts data with params:', queryParams.toString());

    // Fetch the updated best parts data with all parameters
    fetch(`/superadmin/best-parts-data?${queryParams.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Best parts data loaded:', data);

            // Update the best parts content for each division
            updateBestPartsContent(data, division);

            // Hide loading overlay
            hideLoadingOverlay();
        })
        .catch(error => {
            console.error('Error loading best parts data:', error);
            alert('Gagal memuat data part terbaik. Silakan coba lagi.');
            hideLoadingOverlay();
        });
}

/**
 * Update best parts content with the loaded data
 *
 * @param {Object} data - Best parts data from the server
 * @param {string} selectedDivision - The selected division
 */
function updateBestPartsContent(data, selectedDivision = '') {
    // Define the divisions we want to update
    const divisions = ['AC', 'TYRE', 'FABRIKASI'];

    // If a specific division is selected, only update that one
    if (selectedDivision && divisions.includes(selectedDivision.toUpperCase())) {
        updateDivisionContent(selectedDivision.toUpperCase(), data[selectedDivision.toUpperCase()]);
    } else {
        // Otherwise update all divisions
        divisions.forEach(division => {
            if (data[division]) {
                updateDivisionContent(division, data[division]);
            }
        });
    }
}

/**
 * Update content for a specific division
 *
 * @param {string} division - The division to update
 * @param {Object} divisionData - The data for this division
 */
function updateDivisionContent(division, divisionData) {
    // Get the category pane for this division
    const categoryPane = document.getElementById(`category-${division}`);
    if (!categoryPane) return;

    // Get the table body for this division
    const tableBody = categoryPane.querySelector('tbody');
    if (!tableBody) return;

    // Get the empty state element for this division
    const emptyState = categoryPane.querySelector('.empty-state');

    // Update the total revenue display
    const totalRevenueElement = categoryPane.querySelector('h6[style*="color"]');
    if (totalRevenueElement && divisionData && divisionData.total_revenue_with_ppn !== undefined) {
        totalRevenueElement.textContent = `Rp ${formatNumber(divisionData.total_revenue_with_ppn)}`;
    }

    // Check if we have data for this division
    if (divisionData && divisionData.count > 0 && divisionData.items && divisionData.items.length > 0) {
        // Show the table and hide the empty state
        const tableResponsive = tableBody.closest('.table-responsive');
        if (tableResponsive) {
            tableResponsive.style.display = 'block';
        }
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        // Clear existing rows
        tableBody.innerHTML = '';

        // Add new rows
        divisionData.items.forEach(part => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${part.part_name}</td>
                <td>${part.total_quantity}</td>
                <td>Rp ${formatNumber(part.total_value)}</td>
                <td>${part.contribution_percent}%</td>
            `;
            tableBody.appendChild(row);
        });
    } else {
        // Hide the table and show the empty state
        const tableResponsive = tableBody.closest('.table-responsive');
        if (tableResponsive) {
            tableResponsive.style.display = 'none';
        }
        if (emptyState) {
            emptyState.style.display = 'block';
        }
    }

    // Also update the revenue card for this division
    const revenueCard = document.querySelector(`.revenue-card[data-part-type="${division}"] h4`);
    if (revenueCard && divisionData && divisionData.total_revenue_with_ppn !== undefined) {
        revenueCard.textContent = `Rp ${formatNumber(divisionData.total_revenue_with_ppn)}`;
    }
}

/**
 * Initialize best parts settings
 */
function initBestPartsSettings() {
    const saveBestPartsSettingsBtn = document.getElementById('saveBestPartsSettings');
    if (!saveBestPartsSettingsBtn) return;

    saveBestPartsSettingsBtn.addEventListener('click', function() {
        // Show loading overlay
        showLoadingOverlay();

        // Get settings values
        const limit = document.getElementById('bestPartsLimit').value;
        const sortByValue = document.getElementById('sortByValue').checked;
        const sortByQuantity = document.getElementById('sortByQuantity').checked;
        const sortBy = sortByValue ? 'value' : (sortByQuantity ? 'quantity' : 'value');

        // Get date range values
        const startDateInput = document.getElementById('start-date');
        const endDateInput = document.getElementById('end-date');
        const startDate = startDateInput ? startDateInput.value : '';
        const endDate = endDateInput ? endDateInput.value : '';

        // Save settings via AJAX
        fetch('/superadmin/save-best-parts-settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                limit: limit,
                sort_by: sortBy
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Settings saved:', data);

            // Build query parameters for reloading data
            let queryParams = new URLSearchParams();

            // Add date range parameters if available
            if (startDate && endDate) {
                queryParams.append('start_date', startDate);
                queryParams.append('end_date', endDate);
            }

            // Reload the page with the new settings
            window.location.href = `${window.location.pathname}?${queryParams.toString()}`;
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            alert('Gagal menyimpan pengaturan. Silakan coba lagi.');
            hideLoadingOverlay();
        });
    });
}

/**
 * Format number with thousand separators
 *
 * @param {number} number - The number to format
 * @returns {string} - The formatted number
 */
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(Math.round(number));
}

/**
 * Show loading overlay
 */
function showLoadingOverlay() {
    const overlay = document.getElementById('skeleton-loader');
    const content = document.querySelector('.content-wrapper');

    if (overlay) overlay.style.display = 'block';
    if (content) content.style.display = 'none';
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    const overlay = document.getElementById('skeleton-loader');
    const content = document.querySelector('.content-wrapper');

    if (overlay) overlay.style.display = 'none';
    if (content) content.style.display = 'block';
}
