/**
 * Superadmin Dashboard Units Modal
 *
 * Handles the display of unit transactions in a modal when clicking on status cards
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add click event listeners to status cards
    const belumPoCards = document.querySelectorAll('.status-card:first-child');
    const prosesInvoiceCards = document.querySelectorAll('.status-card:last-child');

    belumPoCards.forEach(card => {
        card.style.cursor = 'pointer';
        card.addEventListener('click', function() {
            const siteCard = this.closest('.site-card');
            const siteId = siteCard ? siteCard.getAttribute('data-site-id') : null;
            const siteName = siteCard ? siteCard.querySelector('.card-title').textContent : 'Semua Site';
            openUnitsModal('belum-po', siteId, siteName);
        });
    });

    prosesInvoiceCards.forEach(card => {
        card.style.cursor = 'pointer';
        card.addEventListener('click', function() {
            const siteCard = this.closest('.site-card');
            const siteId = siteCard ? siteCard.getAttribute('data-site-id') : null;
            const siteName = siteCard ? siteCard.querySelector('.card-title').textContent : 'Semua Site';
            openUnitsModal('proses-invoice', siteId, siteName);
        });
    });

    /**
     * Open the units modal and load data via AJAX
     *
     * @param {string} status - The status to filter units by ('belum-po' or 'proses-invoice')
     * @param {string|null} siteId - The site ID to filter by, or null for all sites
     * @param {string} siteName - The name of the site for display in the modal title
     */
    function openUnitsModal(status, siteId, siteName) {
        try {
            // Get the selected date range inputs
            const startDateInput = document.getElementById('start-date');
            const endDateInput = document.getElementById('end-date');

            // Get the selected month from date inputs or fallback to current month
            let selectedMonth = '';

            if (startDateInput && startDateInput.value) {
                // Extract year and month from the start date
                const startDate = new Date(startDateInput.value);
                selectedMonth = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;
            } else {
                // Fallback to current month
                const now = new Date();
                selectedMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
            }

            // Set modal title based on status and site
            let modalTitle = '';
            if (status === 'belum-po') {
                modalTitle = `Daftar Unit Belum PO - ${siteName}`;
            } else if (status === 'proses-invoice') {
                modalTitle = `Daftar Unit Ready PO Belum Invoice - ${siteName}`;
            }

            const modalTitleElement = document.getElementById('unitsModalLabel');
            if (modalTitleElement) {
                modalTitleElement.textContent = modalTitle;
            }

            // Get modal elements
            const modalElement = document.getElementById('unitsModal');
            if (!modalElement) {
                console.error('Units modal element not found');
                return;
            }

            // Show loading spinner, hide content
            const loadingSpinner = modalElement.querySelector('.spinner-border')?.parentElement;
            const contentContainer = document.getElementById('unitsModalContent');
            const tableBody = document.getElementById('unitsTableBody');

            if (!tableBody) {
                console.error('Units table body element not found');
                return;
            }

            if (loadingSpinner) loadingSpinner.style.display = 'block';
            if (contentContainer) contentContainer.style.display = 'none';

            // Show the modal
            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            // Build the URL with query parameters
            let url = `/superadmin/units-by-status/${status}?month=${selectedMonth}`;
            if (siteId) {
                url += `&site_id=${siteId}`;
            }

            // Fetch units data via AJAX
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Clear existing table rows
                    tableBody.innerHTML = '';

                    // Check if there are transactions to display
                    if (data.transactions && data.transactions.length > 0) {
                        // Render the units table
                        data.transactions.forEach((transaction, index) => {
                            const row = document.createElement('tr');

                            // Safely access properties with fallbacks
                            const unitCode = transaction.unit_code || 'N/A';
                            const unitName = transaction.unit_name || 'N/A';
                            const totalPrice = transaction.total_price || 0;
                            const status = transaction.status || 'Unknown';
                            const notes = transaction.notes || '-';
                            const createdAt = transaction.created_at || '-';

                            row.innerHTML = `
                                <td>${index + 1}</td>
                                <td>${unitCode}</td>
                                <td>${unitName}</td>
                                <td>Rp ${formatNumber(totalPrice)}</td>
                                <td><span class="badge ${getStatusBadgeClass(status)}">${status}</span></td>
                                <td>${notes}</td>
                                <td>${createdAt}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info detail-parts-btn" data-transaction-id="${transaction.id}">
                                        <i class="mdi mdi-eye"></i>
                                    </button>
                                </td>
                            `;
                            tableBody.appendChild(row);

                            // Store transaction data for detail modal
                            const detailButton = row.querySelector('.detail-parts-btn');
                            if (detailButton) {
                                detailButton.addEventListener('click', function() {
                                    showPartsDetailModal(transaction);
                                });
                            }
                        });
                    } else {
                        // Display a message if no transactions found
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td colspan="8" class="text-center">Tidak ada data unit yang ditemukan</td>
                        `;
                        tableBody.appendChild(row);
                    }

                    // Hide loading spinner, show content
                    if (loadingSpinner) loadingSpinner.style.display = 'none';
                    if (contentContainer) contentContainer.style.display = 'block';
                })
                .catch(error => {
                    console.error('Error fetching units data:', error);
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="text-center text-danger">
                                <i class="mdi mdi-alert-circle-outline me-2"></i>
                                Gagal memuat data. Silakan coba lagi.
                            </td>
                        </tr>
                    `;
                    if (loadingSpinner) loadingSpinner.style.display = 'none';
                    if (contentContainer) contentContainer.style.display = 'block';
                });
        } catch (error) {
            console.error('Error opening units modal:', error);
            alert('Terjadi kesalahan saat membuka modal. Silakan coba lagi.');
        }
    }

    /**
     * Format a number as currency (with thousands separator)
     *
     * @param {number} number - The number to format
     * @return {string} The formatted number
     */
    function formatNumber(number) {
        return new Intl.NumberFormat('id-ID').format(number);
    }

    /**
     * Get the appropriate badge class for a status
     *
     * @param {string} status - The status value
     * @return {string} The CSS class for the badge
     */
    function getStatusBadgeClass(status) {
        switch (status) {
            case 'On Process': return 'bg-primary';
            case 'MR': return 'bg-info';
            case 'Pending': return 'bg-warning text-dark';
            case 'Ready WO': return 'bg-success';
            case 'Ready PO': return 'bg-success';
            case 'Selesai': return 'bg-dark';
            case 'perbaikan': return 'bg-danger';
            default: return 'bg-secondary';
        }
    }

    /**
     * Show the parts detail modal for a transaction
     *
     * @param {Object} transaction - The transaction object containing parts data
     */
    function showPartsDetailModal(transaction) {
        try {
            // Set transaction details in the modal
            const detailUnitCode = document.getElementById('detail-unit-code');
            const detailUnitName = document.getElementById('detail-unit-name');
            const detailStatus = document.getElementById('detail-status');
            const detailNotes = document.getElementById('detail-notes');
            const partsDetailModalLabel = document.getElementById('partsDetailModalLabel');

            if (detailUnitCode) detailUnitCode.textContent = transaction.unit_code || 'N/A';
            if (detailUnitName) detailUnitName.textContent = transaction.unit_name || 'N/A';
            if (detailStatus) detailStatus.innerHTML = `<span class="badge ${getStatusBadgeClass(transaction.status)}">${transaction.status || 'Unknown'}</span>`;
            if (detailNotes) detailNotes.textContent = transaction.notes || '-';

            // Update modal title
            if (partsDetailModalLabel) {
                partsDetailModalLabel.textContent = `Detail Part Unit: ${transaction.unit_code || 'N/A'} - ${transaction.unit_name || 'N/A'}`;
            }

            // Clear existing table rows
            const tableBody = document.getElementById('partsDetailTableBody');
            if (!tableBody) {
                console.error('Parts detail table body element not found');
                return;
            }

            tableBody.innerHTML = '';

            // Check if there are parts to display
            if (transaction.parts && transaction.parts.length > 0) {
                let totalPrice = 0;

                // Render the parts table
                transaction.parts.forEach((part, index) => {
                    const row = document.createElement('tr');

                    // Safely access properties with fallbacks
                    const partCode = part.part_code || 'N/A';
                    const partName = part.part_name || 'N/A';
                    const quantity = part.quantity || 0;
                    const price = part.price || 0;
                    const total = part.total || 0;

                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${partCode}</td>
                        <td>${partName}</td>
                        <td>${quantity}</td>
                        <td>Rp ${formatNumber(price)}</td>
                        <td>Rp ${formatNumber(total)}</td>
                    `;
                    tableBody.appendChild(row);

                    // Add to total price
                    totalPrice += total;
                });

                // Update total price
                const partsTotalPrice = document.getElementById('parts-total-price');
                if (partsTotalPrice) {
                    partsTotalPrice.textContent = `Rp ${formatNumber(totalPrice)}`;
                }
            } else {
                // Display a message if no parts found
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="6" class="text-center">Tidak ada data part yang ditemukan</td>
                `;
                tableBody.appendChild(row);

                // Reset total price
                const partsTotalPrice = document.getElementById('parts-total-price');
                if (partsTotalPrice) {
                    partsTotalPrice.textContent = 'Rp 0';
                }
            }

            // Show the modal
            const modalElement = document.getElementById('partsDetailModal');
            if (!modalElement) {
                console.error('Parts detail modal element not found');
                return;
            }

            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } catch (error) {
            console.error('Error showing parts detail modal:', error);
            alert('Terjadi kesalahan saat menampilkan detail part. Silakan coba lagi.');
        }
    }
});
